<template>
  <view class="show-more">
    <view
      ref="contentRef"
      class="content"
      :class="{
        'content-collapsed': !isExpanded && shouldClamp,
        'content-expanded': isExpanded || !shouldClamp
      }"
      :style="contentStyle"
    >
      <slot></slot>
      <!-- 渐变遮罩 -->
      <view
        v-if="!isExpanded && shouldClamp && showShadow"
        class="gradient-mask"
      ></view>
    </view>
    <view
      v-if="showToggle"
      class="toggle-btn"
      @click="toggleExpanded"
    >
      <text class="toggle-text">{{ isExpanded ? hideText : showText }}</text>
      <uni-icons
        :type="isExpanded ? 'arrow-up' : 'arrow-down'"
        size="14"
        color="#666"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, getCurrentInstance } from 'vue'

interface Props {
  // 最大显示行数，超出后可折叠
  maxLines?: number
  // 展开时显示的文本
  showText?: string
  // 收起时显示的文本
  hideText?: string
  // 行高，用于更精确的计算（rpx）
  lineHeight?: number
  // 是否显示渐变遮罩
  showGradient?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxLines: 3,
  showText: '显示更多',
  hideText: '收起',
  lineHeight: 40, // 默认行高 40rpx
  showGradient: true
})

const isExpanded = ref(false)
const showToggle = ref(false)
const showShadow = ref(false)
const shouldClamp = ref(false)
const contentRef = ref<any>(null)
const contentHeight = ref(0)
const maxAllowedHeight = ref(0)

// 计算样式
const contentStyle = computed(() => {
  const style: any = {}

  if (!isExpanded.value && shouldClamp.value) {
    // 使用固定高度限制
    style.maxHeight = `${maxAllowedHeight.value}rpx`
    style.overflow = 'hidden'
  }

  return style
})

// 切换展开/收起状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// 检查内容是否超出指定行数
const checkContentOverflow = async () => {
  if (!props.maxLines) {
    showToggle.value = false
    shouldClamp.value = false
    return
  }

  // 计算最大允许高度
  maxAllowedHeight.value = props.maxLines * props.lineHeight

  await nextTick()

  // 延迟执行，确保DOM渲染完成
  setTimeout(() => {
    measureContent()
  }, 100)
}

// 测量内容高度
const measureContent = () => {
  const instance = getCurrentInstance()
  const query = uni.createSelectorQuery().in(instance)

  query.select('.content').boundingClientRect((res: any) => {
    if (res && res.height) {
      contentHeight.value = res.height
      const isOverflow = res.height > maxAllowedHeight.value

      shouldClamp.value = isOverflow
      showToggle.value = isOverflow
      showShadow.value = isOverflow && props.showGradient
    }
  }).exec()
}

// 监听内容变化，重新计算是否需要显示展开按钮
onMounted(() => {
  checkContentOverflow()

  // 延迟检查，确保内容渲染完成
  setTimeout(() => {
    checkContentOverflow()
  }, 200)

  // 再延迟一次确保渲染完成
  setTimeout(() => {
    checkContentOverflow()
  }, 500)
})

// 暴露方法给父组件
defineExpose({
  checkContentOverflow,
  toggleExpanded
})
</script>

<style scoped lang="scss">
.show-more {
  .content {
    position: relative;
    transition: max-height 0.3s ease-in-out;

    &.content-collapsed {
      overflow: hidden;
    }

    &.content-expanded {
      max-height: none !important;
    }
  }

  .gradient-mask {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60rpx;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
    pointer-events: none;
  }

  .toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
    color: #666;
    font-size: 28rpx;
    cursor: pointer;

    .toggle-text {
      margin-right: 10rpx;
    }

    &:active {
      opacity: 0.7;
    }
  }
}
</style>